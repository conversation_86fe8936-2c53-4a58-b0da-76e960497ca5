<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.geometry.Insets?>

<?import javafx.collections.FXCollections?>
<?import java.lang.String?>
<BorderPane xmlns="http://javafx.com/javafx"
            xmlns:fx="http://javafx.com/fxml"
            fx:controller="org.demo.demo.controller.RechercheController"
            prefHeight="400.0" prefWidth="600.0"
            stylesheets="@css/global.css,@css/recherche.css">

    <top>
        <fx:include source="navbar.fxml"/>
    </top>

    <center>
        <VBox styleClass="page-content" spacing="20" alignment="CENTER">
            <padding>
                <Insets top="20" right="20" bottom="20" left="20"/>
            </padding>
        <!-- Section de recherche améliorée -->
        <VBox styleClass="search-section" spacing="20" alignment="CENTER">
            <Label text="Recherche avancée" styleClass="title-small"/>
            <HBox spacing="15" alignment="CENTER">
                <TextField fx:id="searchField" promptText="Rechercher par description" styleClass="search-field"/>
                <ComboBox fx:id="typeComboBox" promptText="Type" styleClass="modern-combo-box">
                    <items>
                        <FXCollections fx:factory="observableArrayList">
                            <String fx:value="Tout"/>
                            <String fx:value="Proto"/>
                            <String fx:value="Série"/>
                        </FXCollections>
                    </items>
                </ComboBox>
                <Button text="🔍 Rechercher" onAction="#onSearchClicked"/>
            </HBox>
        </VBox>

            <!-- Messages de statut -->
            <Label fx:id="noResultLabel" text="Aucun résultat trouvé." visible="false" styleClass="no-result-message" />
            <Label fx:id="loadingLabel" text="Chargement en cours..." visible="false" styleClass="loading-message"/>

            <!-- Conteneur de résultats -->
            <VBox styleClass="results-container" spacing="15">
                <Label text="Résultats de la recherche" styleClass="title-small"/>
                <StackPane prefHeight="350" prefWidth="900">
                    <TableView fx:id="resultTable" prefHeight="350" prefWidth="900" styleClass="results-table">
                        <columns>
                            <TableColumn fx:id="nomColumn" text="Nomenclature" prefWidth="150"/>
                            <TableColumn fx:id="descriptionColumn" text="Dec. PSA" prefWidth="200"/>
                            <TableColumn fx:id="protoColumn" text="Prix unitaire proto (€)" prefWidth="180"/>
                            <TableColumn fx:id="serieColumn" text="Prix unitaire série (€)" prefWidth="180"/>
                            <TableColumn fx:id="nomFichierColumn" text="Nom du fichier" prefWidth="190"/>
                        </columns>
                    </TableView>

                    <TableView fx:id="pdfResultTable" prefHeight="350" prefWidth="900" styleClass="results-table">
                        <columns>
                            <TableColumn fx:id="pdfRefColumn" text="Référence" prefWidth="200"/>
                            <TableColumn fx:id="pdfNomFichierColumn" text="Nom du fichier" prefWidth="400"/>
                            <TableColumn fx:id="pdfActionColumn" text="Télécharger" prefWidth="300"/>
                        </columns>
                    </TableView>
                </StackPane>
            </VBox>

        </VBox>
    </center>
</BorderPane>
