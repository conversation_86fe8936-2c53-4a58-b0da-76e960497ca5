<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.layout.*?>
<?import javafx.scene.control.*?>
<?import javafx.geometry.Insets?>
<?import org.kordamp.ikonli.javafx.FontIcon?>

<BorderPane xmlns:fx="http://javafx.com/fxml"
            fx:controller="org.demo.demo.controller.AddFileController"
            stylesheets="@css/global.css,@css/addfile.css">

  <!-- Barre de navigation en haut -->
  <top>
    <fx:include source="navbar.fxml"/>
  </top>

  <!-- Contenu principal -->
  <center>
    <VBox styleClass="page-content" spacing="25" alignment="CENTER">
      <padding>
        <Insets top="30" right="30" bottom="30" left="30"/>
      </padding>

      <!-- Titre -->
      <Label fx:id="Text" text="Ajoutez un fichier à votre base de données" styleClass="header-label"/>

      <!-- Sélecteur de fichier amélioré -->
      <VBox styleClass="file-selection-container" spacing="15" alignment="CENTER">
        <Label text="Sélectionnez un fichier à importer" styleClass="subtitle"/>
        <HBox spacing="15" alignment="CENTER">
          <TextField fx:id="filePathField" promptText="Chemin du fichier" editable="false" styleClass="file-path-field"/>
          <Button onAction="#onDownloadButtonClick" styleClass="icon-button">
            <tooltip>
              <Tooltip text="Télécharger un fichier"/>
            </tooltip>
            <graphic>
              <FontIcon iconLiteral="bi-upload" styleClass="upload-icon"/>
            </graphic>
          </Button>
        </HBox>
      </VBox>

      <!-- Bouton d'ajout -->
      <Button text="Ajouter" onAction="#onAddButtonClick" styleClass="main-button"/>

      <!-- Tableau des fichiers amélioré -->
      <VBox styleClass="results-container" spacing="15">
        <Label text="Aperçu des données" styleClass="title-small"/>
        <TableView fx:id="tableView" prefHeight="300" prefWidth="800" styleClass="modern-table">
          <columns>
            <TableColumn fx:id="column1" text="Nomenclature" prefWidth="200"/>
            <TableColumn fx:id="column2" text="Dec. PSA" prefWidth="200"/>
            <TableColumn fx:id="column3" text="Prix unitaire proto (€)" prefWidth="200"/>
            <TableColumn fx:id="column4" text="Prix unitaire série (€)" prefWidth="200"/>
          </columns>
        </TableView>
      </VBox>

      <!-- Bouton d'enregistrement -->
      <Button fx:id="saveButton"
              text="💾 Enregistrer dans la base de données"
              onAction="#onSaveToDatabaseClick"
              styleClass="save-button"/>

    </VBox>
  </center>

</BorderPane>
