/* ===============================================
   FICHIER CSS GLOBAL - THEME PROFESSIONNEL
   =============================================== */

/* ======= VARIABLES CSS (Couleurs du thème) ======= */
.root {
    /* Palette de couleurs principale */
    -primary-color: #0072BC;
    -primary-light: #00AEEF;
    -primary-dark: #004A7C;
    -secondary-color: #2C3E50;
    -accent-color: #E74C3C;
    -success-color: #27AE60;
    -warning-color: #F39C12;
    
    /* Couleurs neutres */
    -background-light: #F8F9FA;
    -background-gradient-start: #E3F2FD;
    -background-gradient-end: #EDE7F6;
    -surface-color: #FFFFFF;
    -text-primary: #2C3E50;
    -text-secondary: #7F8C8D;
    -text-light: #BDC3C7;
    
    /* Ombres */
    -shadow-light: rgba(0, 0, 0, 0.1);
    -shadow-medium: rgba(0, 0, 0, 0.15);
    -shadow-strong: rgba(0, 0, 0, 0.25);
    
    /* Rayons de bordure */
    -radius-small: 8px;
    -radius-medium: 12px;
    -radius-large: 20px;
    -radius-xl: 25px;
    
    /* Espacements */
    -spacing-xs: 5px;
    -spacing-sm: 10px;
    -spacing-md: 15px;
    -spacing-lg: 20px;
    -spacing-xl: 30px;
    -spacing-xxl: 40px;
}

/* ======= STYLES DE BASE ======= */
.application-root {
    -fx-font-family: "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
    -fx-font-size: 14px;
    -fx-text-fill: -text-primary;
}

/* ======= FOND GLOBAL AMÉLIORÉ ======= */
.modern-background {
    -fx-background-color: linear-gradient(135deg, -background-gradient-start 0%, -background-gradient-end 100%);
    -fx-background-size: 400% 400%;
}

/* ======= CARTES MODERNES ======= */
.modern-card {
    -fx-background-color: -surface-color;
    -fx-background-radius: -radius-large;
    -fx-border-radius: -radius-large;
    -fx-effect: dropshadow(gaussian, -shadow-medium, 20, 0.3, 0, 8);
    -fx-padding: -spacing-xxl;
}

.modern-card:hover {
    -fx-effect: dropshadow(gaussian, -shadow-strong, 25, 0.4, 0, 12);
}

/* ======= BOUTONS MODERNES ======= */
.btn-primary {
    -fx-background-color: linear-gradient(to bottom, -primary-color, -primary-dark);
    -fx-text-fill: white;
    -fx-font-weight: 600;
    -fx-font-size: 14px;
    -fx-padding: 12px 24px;
    -fx-background-radius: -radius-medium;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(0, 114, 188, 0.4), 10, 0, 0, 4);
    -fx-border-width: 0;
}

.btn-primary:hover {
    -fx-background-color: linear-gradient(to bottom, -primary-light, -primary-color);
    -fx-effect: dropshadow(gaussian, rgba(0, 174, 239, 0.6), 15, 0, 0, 6);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

.btn-primary:pressed {
    -fx-background-color: -primary-dark;
    -fx-scale-x: 0.98;
    -fx-scale-y: 0.98;
}

.btn-secondary {
    -fx-background-color: transparent;
    -fx-text-fill: -primary-color;
    -fx-font-weight: 600;
    -fx-font-size: 14px;
    -fx-padding: 12px 24px;
    -fx-background-radius: -radius-medium;
    -fx-border-color: -primary-color;
    -fx-border-width: 2px;
    -fx-border-radius: -radius-medium;
    -fx-cursor: hand;
}

.btn-secondary:hover {
    -fx-background-color: -primary-color;
    -fx-text-fill: white;
}

.btn-icon {
    -fx-background-color: transparent;
    -fx-padding: 8px;
    -fx-background-radius: 50%;
    -fx-cursor: hand;
}

.btn-icon:hover {
    -fx-background-color: rgba(0, 114, 188, 0.1);
}

/* ======= CHAMPS DE SAISIE MODERNES ======= */
.modern-text-field {
    -fx-background-color: -surface-color;
    -fx-border-color: #E0E6ED;
    -fx-border-width: 2px;
    -fx-border-radius: -radius-medium;
    -fx-background-radius: -radius-medium;
    -fx-padding: 12px 16px;
    -fx-font-size: 14px;
    -fx-text-fill: -text-primary;
    -fx-effect: dropshadow(gaussian, -shadow-light, 5, 0, 0, 2);
}

.modern-text-field:focused {
    -fx-border-color: -primary-color;
    -fx-effect: dropshadow(gaussian, rgba(0, 114, 188, 0.3), 8, 0, 0, 3);
}

.modern-combo-box {
    -fx-background-color: -surface-color;
    -fx-border-color: #E0E6ED;
    -fx-border-width: 2px;
    -fx-border-radius: -radius-medium;
    -fx-background-radius: -radius-medium;
    -fx-padding: 8px 12px;
    -fx-font-size: 14px;
}

.modern-combo-box:focused {
    -fx-border-color: -primary-color;
}

/* ======= TABLEAUX MODERNES ======= */
.modern-table {
    -fx-background-color: -surface-color;
    -fx-border-color: #E0E6ED;
    -fx-border-width: 1px;
    -fx-border-radius: -radius-medium;
    -fx-background-radius: -radius-medium;
    -fx-effect: dropshadow(gaussian, -shadow-light, 8, 0, 0, 4);
}

.modern-table .column-header {
    -fx-background-color: linear-gradient(to bottom, #F8F9FA, #E9ECEF);
    -fx-text-fill: -text-primary;
    -fx-font-weight: 600;
    -fx-font-size: 13px;
    -fx-padding: 12px 8px;
}

.modern-table .table-row-cell {
    -fx-background-color: -surface-color;
    -fx-border-color: transparent;
    -fx-padding: 8px;
}

.modern-table .table-row-cell:hover {
    -fx-background-color: rgba(0, 114, 188, 0.05);
}

.modern-table .table-row-cell:selected {
    -fx-background-color: rgba(0, 114, 188, 0.1);
}

/* ======= TYPOGRAPHIE ======= */
.title-large {
    -fx-font-size: 32px;
    -fx-font-weight: 700;
    -fx-text-fill: -text-primary;
}

.title-medium {
    -fx-font-size: 24px;
    -fx-font-weight: 600;
    -fx-text-fill: -text-primary;
}

.title-small {
    -fx-font-size: 18px;
    -fx-font-weight: 600;
    -fx-text-fill: -text-primary;
}

.subtitle {
    -fx-font-size: 16px;
    -fx-font-weight: 400;
    -fx-text-fill: -text-secondary;
    -fx-line-spacing: 2px;
}

.body-text {
    -fx-font-size: 14px;
    -fx-font-weight: 400;
    -fx-text-fill: -text-primary;
}

.caption {
    -fx-font-size: 12px;
    -fx-font-weight: 400;
    -fx-text-fill: -text-secondary;
}

/* ======= ANIMATIONS ET TRANSITIONS ======= */
.fade-in {
    -fx-opacity: 0;
}

.slide-up {
    -fx-translate-y: 20;
    -fx-opacity: 0;
}

/* ======= ÉTATS DE CHARGEMENT ======= */
.loading-indicator {
    -fx-text-fill: -text-secondary;
    -fx-font-style: italic;
    -fx-font-size: 14px;
}

/* ======= MESSAGES D'ÉTAT ======= */
.success-message {
    -fx-text-fill: -success-color;
    -fx-font-weight: 600;
}

.error-message {
    -fx-text-fill: -accent-color;
    -fx-font-weight: 600;
}

.warning-message {
    -fx-text-fill: -warning-color;
    -fx-font-weight: 600;
}

/* ======= CONTENEURS ======= */
.content-container {
    -fx-padding: -spacing-xl;
    -fx-spacing: -spacing-lg;
}

.section-container {
    -fx-padding: -spacing-lg;
    -fx-spacing: -spacing-md;
}
