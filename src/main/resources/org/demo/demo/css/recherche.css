/* ===============================================
   PAGE DE RECHERCHE - DESIGN MODERNE
   =============================================== */

/* Fond général de la page */
.page-content {
    -fx-background-color: linear-gradient(135deg, #E3F2FD 0%, #EDE7F6 25%, #E8F5E8 50%, #FFF3E0 75%, #E3F2FD 100%);
    -fx-background-size: 400% 400%;
    -fx-padding: 40;
    -fx-spacing: 30;
    -fx-alignment: center;
}

/* Conteneur principal avec carte */
.search-container {
    -fx-background-color: rgba(255, 255, 255, 0.95);
    -fx-background-radius: 20;
    -fx-border-radius: 20;
    -fx-padding: 40;
    -fx-spacing: 25;
    -fx-effect: dropshadow(gaussian, rgba(0, 114, 188, 0.15), 20, 0.3, 0, 8);
    -fx-border-width: 1;
    -fx-border-color: rgba(255, 255, 255, 0.3);
    -fx-max-width: 1000;
}

/* Section de recherche */
.search-section {
    -fx-background-color: rgba(0, 114, 188, 0.05);
    -fx-background-radius: 15;
    -fx-padding: 25;
    -fx-spacing: 20;
    -fx-border-width: 2;
    -fx-border-color: rgba(0, 114, 188, 0.1);
    -fx-border-radius: 15;
}

/* Champ de recherche moderne */
.search-field {
    -fx-pref-width: 300px;
    -fx-font-size: 15px;
    -fx-border-radius: 12;
    -fx-background-radius: 12;
    -fx-border-color: #E0E6ED;
    -fx-border-width: 2px;
    -fx-padding: 12 16;
    -fx-background-color: white;
    -fx-text-fill: #2C3E50;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.05), 5, 0, 0, 2);
}

.search-field:focused {
    -fx-border-color: #0072BC;
    -fx-effect: dropshadow(gaussian, rgba(0, 114, 188, 0.3), 8, 0, 0, 3);
}

/* ComboBox moderne */
.modern-combo-box {
    -fx-pref-width: 150px;
    -fx-font-size: 14px;
    -fx-background-color: white;
    -fx-border-color: #E0E6ED;
    -fx-border-width: 2px;
    -fx-border-radius: 12;
    -fx-background-radius: 12;
    -fx-padding: 8 12;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.05), 5, 0, 0, 2);
}

.modern-combo-box:focused {
    -fx-border-color: #0072BC;
}

/* Boutons de la page */
.page-content .button {
    -fx-background-color: linear-gradient(to bottom, #0072BC, #004A7C);
    -fx-text-fill: white;
    -fx-font-weight: 600;
    -fx-font-size: 14px;
    -fx-padding: 12 24;
    -fx-background-radius: 12;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(0, 114, 188, 0.4), 10, 0, 0, 4);
    -fx-border-width: 0;
}

.page-content .button:hover {
    -fx-background-color: linear-gradient(to bottom, #00AEEF, #0072BC);
    -fx-effect: dropshadow(gaussian, rgba(0, 174, 239, 0.6), 12, 0, 0, 5);
    -fx-scale-x: 1.03;
    -fx-scale-y: 1.03;
}

.page-content .button:pressed {
    -fx-scale-x: 0.97;
    -fx-scale-y: 0.97;
}

/* Tableau de résultats moderne */
.results-table {
    -fx-background-color: white;
    -fx-border-color: #E0E6ED;
    -fx-border-width: 1;
    -fx-border-radius: 15;
    -fx-background-radius: 15;
    -fx-table-cell-border-color: transparent;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.08), 12, 0, 0, 4);
}

/* En-têtes de colonnes */
.results-table .column-header {
    -fx-background-color: linear-gradient(to bottom, #F8F9FA, #E9ECEF);
    -fx-text-fill: #2C3E50;
    -fx-font-weight: 600;
    -fx-font-size: 14px;
    -fx-padding: 15 10;
    -fx-border-color: #E0E6ED;
}

.results-table .column-header:first {
    -fx-background-radius: 15 0 0 0;
}

.results-table .column-header:last {
    -fx-background-radius: 0 15 0 0;
}

/* Lignes du tableau */
.results-table .table-row-cell {
    -fx-background-color: white;
    -fx-border-color: transparent;
    -fx-padding: 12 8;
    -fx-font-size: 13px;
}

.results-table .table-row-cell:hover {
    -fx-background-color: rgba(0, 114, 188, 0.05);
}

.results-table .table-row-cell:selected {
    -fx-background-color: rgba(0, 114, 188, 0.1);
    -fx-text-fill: #2C3E50;
}

/* Messages de statut */
.no-result-message {
    -fx-font-size: 16px;
    -fx-text-fill: #E74C3C;
    -fx-font-weight: 500;
    -fx-padding: 20;
    -fx-background-color: rgba(231, 76, 60, 0.1);
    -fx-background-radius: 10;
    -fx-border-color: rgba(231, 76, 60, 0.3);
    -fx-border-width: 1;
    -fx-border-radius: 10;
}

.loading-message {
    -fx-font-size: 14px;
    -fx-text-fill: #7F8C8D;
    -fx-font-style: italic;
    -fx-font-weight: 500;
    -fx-padding: 15;
    -fx-background-color: rgba(127, 140, 141, 0.1);
    -fx-background-radius: 8;
}

/* Conteneur de résultats */
.results-container {
    -fx-background-color: rgba(255, 255, 255, 0.9);
    -fx-background-radius: 15;
    -fx-padding: 20;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.05), 8, 0, 0, 3);
}

/* Bouton de téléchargement spécial */
.download-button {
    -fx-background-color: linear-gradient(to right, #27AE60, #2ECC71);
    -fx-text-fill: white;
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-padding: 8 16;
    -fx-background-radius: 8;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(39, 174, 96, 0.3), 6, 0, 0, 2);
    -fx-border-width: 0;
}

.download-button:hover {
    -fx-background-color: linear-gradient(to right, #2ECC71, #58D68D);
    -fx-effect: dropshadow(gaussian, rgba(46, 204, 113, 0.5), 8, 0, 0, 3);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

/* Indicateur de chargement animé */
.loading-indicator {
    -fx-progress-color: #0072BC;
    -fx-background-color: rgba(0, 114, 188, 0.1);
    -fx-background-radius: 10;
}

/* Filtres avancés */
.filter-section {
    -fx-background-color: rgba(0, 174, 239, 0.05);
    -fx-background-radius: 12;
    -fx-padding: 20;
    -fx-spacing: 15;
    -fx-border-width: 1;
    -fx-border-color: rgba(0, 174, 239, 0.2);
    -fx-border-radius: 12;
}

/* Responsive design */
@media (max-width: 1000px) {
    .search-container {
        -fx-max-width: 100%;
        -fx-padding: 30;
    }

    .search-field {
        -fx-pref-width: 250px;
    }

    .modern-combo-box {
        -fx-pref-width: 120px;
    }
}
