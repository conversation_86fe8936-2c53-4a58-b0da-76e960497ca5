/* ===============================================
   PAGE D'AJOUT DE FICHIER - DESIGN MODERNE
   =============================================== */

/* Fond général de la page avec dégradé animé */
.page-content {
    -fx-background-color: linear-gradient(135deg, #E3F2FD 0%, #EDE7F6 25%, #E8F5E8 50%, #FFF3E0 75%, #E3F2FD 100%);
    -fx-background-size: 400% 400%;
    -fx-padding: 40;
    -fx-spacing: 30;
    -fx-alignment: center;
    -fx-border-radius: 0;
    -fx-background-radius: 0;
}

/* Conteneur principal avec carte */
.content-card {
    -fx-background-color: rgba(255, 255, 255, 0.95);
    -fx-background-radius: 20;
    -fx-border-radius: 20;
    -fx-padding: 40;
    -fx-spacing: 25;
    -fx-effect: dropshadow(gaussian, rgba(0, 114, 188, 0.15), 20, 0.3, 0, 8);
    -fx-border-width: 1;
    -fx-border-color: rgba(255, 255, 255, 0.3);
    -fx-max-width: 900;
}

/* Style du titre principal */
.header-label {
    -fx-font-size: 28px;
    -fx-text-fill: #2C3E50;
    -fx-font-weight: 700;
    -fx-font-family: "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
    -fx-effect: dropshadow(gaussian, rgba(0, 114, 188, 0.2), 3, 0, 0, 1);
}

/* Section de sélection de fichier */
.file-selection-container {
    -fx-background-color: rgba(0, 114, 188, 0.05);
    -fx-background-radius: 15;
    -fx-padding: 25;
    -fx-spacing: 15;
    -fx-border-width: 2;
    -fx-border-color: rgba(0, 114, 188, 0.1);
    -fx-border-radius: 15;
}

/* Champ texte chemin fichier amélioré */
.file-path-field {
    -fx-pref-width: 500px;
    -fx-font-size: 15px;
    -fx-border-radius: 12;
    -fx-background-radius: 12;
    -fx-border-color: #E0E6ED;
    -fx-border-width: 2px;
    -fx-padding: 12 16;
    -fx-background-color: white;
    -fx-text-fill: #2C3E50;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.05), 5, 0, 0, 2);
}

.file-path-field:focused {
    -fx-border-color: #0072BC;
    -fx-effect: dropshadow(gaussian, rgba(0, 114, 188, 0.3), 8, 0, 0, 3);
}

/* Boutons principaux modernisés */
.main-button {
    -fx-background-color: linear-gradient(to bottom, #0072BC, #004A7C);
    -fx-text-fill: white;
    -fx-font-size: 16px;
    -fx-font-weight: 600;
    -fx-padding: 14 28;
    -fx-background-radius: 12;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(0, 114, 188, 0.4), 12, 0, 0, 4);
    -fx-border-width: 0;
}

.main-button:hover {
    -fx-background-color: linear-gradient(to bottom, #00AEEF, #0072BC);
    -fx-effect: dropshadow(gaussian, rgba(0, 174, 239, 0.6), 15, 0, 0, 6);
    -fx-scale-x: 1.03;
    -fx-scale-y: 1.03;
}

.main-button:pressed {
    -fx-scale-x: 0.97;
    -fx-scale-y: 0.97;
}

/* Bouton avec icône upload amélioré */
.icon-button {
    -fx-background-color: rgba(0, 114, 188, 0.1);
    -fx-cursor: hand;
    -fx-padding: 12;
    -fx-background-radius: 50%;
    -fx-border-width: 2;
    -fx-border-color: rgba(0, 114, 188, 0.2);
    -fx-border-radius: 50%;
}

.icon-button:hover {
    -fx-background-color: rgba(0, 114, 188, 0.2);
    -fx-border-color: #0072BC;
    -fx-scale-x: 1.1;
    -fx-scale-y: 1.1;
}

/* Icône upload style */
.upload-icon {
    -fx-icon-size: 28px;
    -fx-fill: #0072BC;
}

/* Style TableView moderne */
.table-view {
    -fx-background-color: white;
    -fx-border-color: #E0E6ED;
    -fx-border-width: 1;
    -fx-border-radius: 15;
    -fx-background-radius: 15;
    -fx-table-cell-border-color: transparent;
    -fx-padding: 0;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.08), 10, 0, 0, 4);
}

.table-column {
    -fx-font-size: 14px;
    -fx-alignment: CENTER_LEFT;
    -fx-text-fill: #2C3E50;
}

/* En-têtes de colonnes */
.table-view .column-header {
    -fx-background-color: linear-gradient(to bottom, #F8F9FA, #E9ECEF);
    -fx-text-fill: #2C3E50;
    -fx-font-weight: 600;
    -fx-font-size: 14px;
    -fx-padding: 15 10;
    -fx-border-color: #E0E6ED;
}

.table-view .column-header:first {
    -fx-background-radius: 15 0 0 0;
}

.table-view .column-header:last {
    -fx-background-radius: 0 15 0 0;
}

/* Lignes du tableau */
.table-view .table-row-cell {
    -fx-background-color: white;
    -fx-border-color: transparent;
    -fx-padding: 12 8;
    -fx-font-size: 14px;
}

.table-view .table-row-cell:hover {
    -fx-background-color: rgba(0, 114, 188, 0.05);
}

.table-view .table-row-cell:selected {
    -fx-background-color: rgba(0, 114, 188, 0.1);
}

/* Bouton de sauvegarde spécial */
.save-button {
    -fx-background-color: linear-gradient(to right, #27AE60, #2ECC71);
    -fx-text-fill: white;
    -fx-font-size: 16px;
    -fx-font-weight: 600;
    -fx-padding: 14 28;
    -fx-background-radius: 12;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(39, 174, 96, 0.4), 12, 0, 0, 4);
    -fx-border-width: 0;
}

.save-button:hover {
    -fx-background-color: linear-gradient(to right, #2ECC71, #58D68D);
    -fx-effect: dropshadow(gaussian, rgba(46, 204, 113, 0.6), 15, 0, 0, 6);
    -fx-scale-x: 1.03;
    -fx-scale-y: 1.03;
}

/* Messages de statut */
.status-message {
    -fx-font-size: 14px;
    -fx-font-weight: 500;
    -fx-padding: 10 15;
    -fx-background-radius: 8;
    -fx-border-radius: 8;
}

.status-success {
    -fx-background-color: rgba(39, 174, 96, 0.1);
    -fx-text-fill: #27AE60;
    -fx-border-color: rgba(39, 174, 96, 0.3);
    -fx-border-width: 1;
}

.status-error {
    -fx-background-color: rgba(231, 76, 60, 0.1);
    -fx-text-fill: #E74C3C;
    -fx-border-color: rgba(231, 76, 60, 0.3);
    -fx-border-width: 1;
}

/* Zone de glisser-déposer */
.drop-zone {
    -fx-background-color: rgba(0, 114, 188, 0.05);
    -fx-border-color: rgba(0, 114, 188, 0.3);
    -fx-border-width: 2;
    -fx-border-style: dashed;
    -fx-border-radius: 15;
    -fx-background-radius: 15;
    -fx-padding: 40;
    -fx-alignment: CENTER;
    -fx-spacing: 15;
}

.drop-zone:hover {
    -fx-background-color: rgba(0, 114, 188, 0.1);
    -fx-border-color: #0072BC;
}

.drop-zone-active {
    -fx-background-color: rgba(0, 174, 239, 0.1);
    -fx-border-color: #00AEEF;
    -fx-border-width: 3;
}

/* Responsive design */
@media (max-width: 1000px) {
    .content-card {
        -fx-max-width: 100%;
        -fx-padding: 30;
    }

    .file-path-field {
        -fx-pref-width: 400px;
    }

    .header-label {
        -fx-font-size: 24px;
    }
}
