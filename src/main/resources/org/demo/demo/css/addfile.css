/* Fond général de la page */
.page-content {
      -fx-background-color: linear-gradient(to bottom right, #e3f2fd, #ede7f6);
    -fx-padding: 30;
    -fx-spacing: 25;
    -fx-alignment: center;
    -fx-border-radius: 15;
    -fx-background-radius: 15;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);
}

/* Style du titre */
.header-label {
    -fx-font-size: 26px;
    -fx-text-fill: #34495e;
    -fx-font-weight: bold;
}

/* Champ texte chemin fichier */
.file-path-field {
    -fx-pref-width: 500px;
    -fx-font-size: 14px;
    -fx-border-radius: 10;
    -fx-background-radius: 10;
    -fx-border-color: #bdc3c7;
    -fx-border-width: 1;
    -fx-padding: 8 12;
    -fx-background-color: white;
}

/* Boutons principaux */
.main-button {
    -fx-background-color: #2980b9;
    -fx-text-fill: white;
    -fx-font-size: 16px;
    -fx-padding: 10 20;
    -fx-background-radius: 15;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(41, 128, 185, 0.5), 8, 0, 0, 2);
}

.main-button:hover {
    -fx-background-color: #3498db;
}

/* Bouton avec icône upload */
.icon-button {
    -fx-background-color: transparent;
    -fx-cursor: hand;
    -fx-padding: 6;
}

.icon-button:hover {
    -fx-background-color: #ecf0f1;
    -fx-background-radius: 50%;
}

/* Icone upload style */
.upload-icon {
    -fx-icon-size: 24px;
    -fx-fill: #2980b9;
}

/* Style TableView */
.table-view {
    -fx-background-color: white;
    -fx-border-color: #dcdcdc;
    -fx-border-width: 1;
    -fx-border-radius: 10;
    -fx-background-radius: 10;
    -fx-table-cell-border-color: transparent;
    -fx-padding: 10;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 5, 0, 0, 1);
}

.table-column {
    -fx-font-size: 14px;
    -fx-alignment: CENTER_LEFT;
    -fx-text-fill: #2c3e50;
}
