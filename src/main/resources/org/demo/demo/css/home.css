/* ===============================================
   PAGE D'ACCUEIL MODERNE ET ATTRACTIVE
   =============================================== */

/* ======= Fond global avec animation ======= */
.background-pane {
    -fx-background-color: linear-gradient(135deg, #E3F2FD 0%, #EDE7F6 25%, #E8F5E8 50%, #FFF3E0 75%, #E3F2FD 100%);
    -fx-background-size: 400% 400%;
}

/* ======= Conteneur principal ======= */
.center-container {
    -fx-padding: 40;
    -fx-min-width: 700;
    -fx-spacing: 50;
}

/* ======= StackPane contenant la carte et bulles ======= */
.stack-container {
    -fx-max-width: 800;
    -fx-pref-width: 700;
}

/* ======= Carte centrale moderne ======= */
.card {
    -fx-background-color: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
    -fx-padding: 50;
    -fx-background-radius: 25;
    -fx-border-radius: 25;
    -fx-effect: dropshadow(gaussian, rgba(0, 114, 188, 0.2), 25, 0.4, 0, 10);
    -fx-border-width: 1;
    -fx-border-color: rgba(255, 255, 255, 0.3);
}

.card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0, 114, 188, 0.3), 35, 0.5, 0, 15);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

/* ======= Titre principal ======= */
.card-title {
    -fx-font-size: 32px;
    -fx-font-weight: 700;
    -fx-text-fill: linear-gradient(to right, #0072BC, #00AEEF);
    -fx-text-alignment: center;
    -fx-font-family: "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
    -fx-line-spacing: 5px;
}

/* ======= Sous-titre élégant ======= */
.card-subtitle {
    -fx-font-size: 18px;
    -fx-text-fill: #2C3E50;
    -fx-text-alignment: center;
    -fx-font-family: "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
    -fx-font-weight: 400;
    -fx-line-spacing: 3px;
    -fx-opacity: 0.9;
}

/* ======= Bulles décoratives améliorées ======= */
.bubble {
    -fx-opacity: 0.15;
}

.bubble-blue {
    -fx-fill: linear-gradient(to bottom right, #0072BC, #00AEEF);
}

.bubble-light {
    -fx-fill: linear-gradient(to bottom right, #00AEEF, #87CEEB);
}

/* Animation des bulles */
.bubble:hover {
    -fx-opacity: 0.25;
    -fx-scale-x: 1.1;
    -fx-scale-y: 1.1;
}

/* ======= Section d'icônes moderne ======= */
.icon-container {
    -fx-background-color: rgba(0, 114, 188, 0.05);
    -fx-background-radius: 15;
    -fx-padding: 20;
    -fx-spacing: 15;
}

.icon-item {
    -fx-alignment: CENTER;
    -fx-spacing: 10;
    -fx-padding: 15;
    -fx-background-radius: 12;
    -fx-cursor: hand;
}

.icon-item:hover {
    -fx-background-color: rgba(0, 114, 188, 0.1);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

.icon-style {
    -fx-font-size: 40px;
    -fx-effect: dropshadow(gaussian, rgba(0, 114, 188, 0.3), 5, 0, 0, 2);
}

.icon-label {
    -fx-font-size: 16px;
    -fx-text-fill: #2C3E50;
    -fx-font-weight: 600;
    -fx-font-family: "Segoe UI", "Roboto", sans-serif;
}

/* ======= Bouton d'action principal ======= */
.cta-button {
    -fx-background-color: linear-gradient(to right, #0072BC, #00AEEF);
    -fx-text-fill: white;
    -fx-font-size: 18px;
    -fx-font-weight: 600;
    -fx-padding: 15 30;
    -fx-background-radius: 25;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(0, 114, 188, 0.4), 15, 0, 0, 5);
    -fx-border-width: 0;
}

.cta-button:hover {
    -fx-background-color: linear-gradient(to right, #00AEEF, #87CEEB);
    -fx-effect: dropshadow(gaussian, rgba(0, 174, 239, 0.6), 20, 0, 0, 8);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

.cta-button:pressed {
    -fx-scale-x: 0.95;
    -fx-scale-y: 0.95;
}

/* ======= Section de statistiques/informations ======= */
.stats-container {
    -fx-background-color: rgba(255, 255, 255, 0.7);
    -fx-background-radius: 15;
    -fx-padding: 20;
    -fx-spacing: 20;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 10, 0, 0, 3);
}

.stat-item {
    -fx-alignment: CENTER;
    -fx-spacing: 5;
}

.stat-number {
    -fx-font-size: 24px;
    -fx-font-weight: 700;
    -fx-text-fill: #0072BC;
}

.stat-label {
    -fx-font-size: 12px;
    -fx-text-fill: #7F8C8D;
    -fx-font-weight: 500;
}

/* ======= Animations et transitions ======= */
@keyframes fadeInUp {
    from {
        -fx-opacity: 0;
        -fx-translate-y: 30;
    }
    to {
        -fx-opacity: 1;
        -fx-translate-y: 0;
    }
}

.fade-in-up {
    -fx-animation: fadeInUp 0.8s ease-out;
}

/* ======= Responsive design ======= */
@media (max-width: 900px) {
    .card-title {
        -fx-font-size: 28px;
    }

    .card-subtitle {
        -fx-font-size: 16px;
    }

    .icon-style {
        -fx-font-size: 32px;
    }

    .center-container {
        -fx-padding: 20;
    }
}
