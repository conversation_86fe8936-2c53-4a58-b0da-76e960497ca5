/* ===============================================
   NAVBAR MODERNE ET PROFESSIONNELLE
   =============================================== */

/* Style pour la barre de navigation avec dégradé moderne */
.navbar {
    -fx-background-color: linear-gradient(135deg, #0072BC 0%, #00AEEF 50%, #0072BC 100%);
    -fx-background-size: 200% 200%;
    -fx-padding: 15px 25px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.2), 10, 0, 0, 3);
    -fx-border-width: 0 0 3px 0;
    -fx-border-color: rgba(255, 255, 255, 0.2);
}

/* Animation subtile du dégradé */
.navbar:hover {
    -fx-background-position: 100% 0%;
}

/* Style pour les boutons de la navbar */
.navbar-button {
    -fx-background-color: transparent;
    -fx-text-fill: white;
    -fx-font-weight: 600;
    -fx-font-size: 14px;
    -fx-padding: 12px 20px;
    -fx-min-width: 100px;
    -fx-background-radius: 8px;
    -fx-cursor: hand;
    -fx-border-width: 0;
    -fx-effect: null;
}

/* Effet hover pour les boutons */
.navbar-button:hover {
    -fx-background-color: rgba(255, 255, 255, 0.15);
    -fx-effect: dropshadow(gaussian, rgba(255, 255, 255, 0.3), 5, 0, 0, 0);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

/* Effet pressed pour les boutons */
.navbar-button:pressed {
    -fx-background-color: rgba(255, 255, 255, 0.25);
    -fx-scale-x: 0.95;
    -fx-scale-y: 0.95;
}

/* Style pour le bouton actif/sélectionné */
.navbar-button.active {
    -fx-background-color: rgba(255, 255, 255, 0.2);
    -fx-border-width: 0 0 2px 0;
    -fx-border-color: white;
}

/* Style spécial pour le bouton menu avec icône */
.navbar-button.menu-button {
    -fx-min-width: 50px;
    -fx-padding: 12px;
    -fx-background-radius: 50%;
}

.navbar-button.menu-button:hover {
    -fx-background-color: rgba(255, 255, 255, 0.2);
    -fx-rotate: 90;
}

/* Séparateur visuel entre les sections */
.navbar-separator {
    -fx-background-color: rgba(255, 255, 255, 0.3);
    -fx-pref-width: 1px;
    -fx-pref-height: 30px;
    -fx-margin: 0 10px;
}

/* Conteneur pour grouper les boutons */
.navbar-group {
    -fx-spacing: 5px;
    -fx-alignment: CENTER_LEFT;
}

/* Style pour le logo/titre de l'application */
.navbar-title {
    -fx-text-fill: white;
    -fx-font-size: 18px;
    -fx-font-weight: 700;
    -fx-padding: 0 20px 0 0;
}

/* Responsive design pour petits écrans */
@media (max-width: 800px) {
    .navbar-button {
        -fx-min-width: 60px;
        -fx-padding: 10px 15px;
        -fx-font-size: 12px;
    }

    .navbar-title {
        -fx-font-size: 16px;
    }
}
